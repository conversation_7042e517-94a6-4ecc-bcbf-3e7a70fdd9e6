
# Angel AI Meeting Assistant

Angel is a desktop application that helps you in meetings by providing **real-time transcription** and **AI-generated answers**.
two ways to acces it: 
---

##Option 1️⃣ Open Source Version (above repo)

This is the **open-source edition** of Angel.
It supports both **OpenAI API** and **Google Gemini API**.
You run it from your **terminal or IDE** with your **own API key(s)**.
**No API key is included by default** —

### ✨ Features (Open Source)
- **Transcription** – Instant speech-to-text conversion  
- **AI-Powered Answers** – Concise and context-aware responses  
- **Always-on-Top** – Keep Angel visible over other apps  
- **Screen Sharing Stealth Mode** – Hide Angel instantly during recordings  
- **Minimalist UI** – Focused on function, not clutter  

### 📦 Quick Start (Clone, Install, Run)
**Prerequisites:**
- Node.js 18+ and npm  
- macOS 10.14+ (Intel or Apple Silicon) or Windows 10+ (64-bit)

**Steps:**

git clone https://github.com/<your-org>/angel-opensource.git
cd angel-opensource
npm install
npm start

🔑 Bring Your Own API Key (OpenAI & Gemini)
	1.	In the app: Click gear icon → Choose provider → Paste API key(s) → Save

Get your keys from:
- OpenAI: https://platform.openai.com/account/api-keys
- Gemini: https://makersuite.google.com/app/apikey (Free!)

🔧 Provider Configuration:
	•	OpenAI: Supports GPT-4o, GPT-4o-mini, GPT-3.5-turbo (requires OpenAI key only)
	•	Gemini: Supports Gemini 1.5 Flash, Gemini 1.5 Pro, Gemini Pro (requires Gemini key only)
	•	Audio transcription: Optional OpenAI key for voice input (uses Whisper)
	•	Text-only mode: Works with just your chosen provider's key
	•	Full voice mode: Requires OpenAI key for transcription + your chosen provider's key

Best practices:
	•	Never commit keys to GitHub
	•	Rotate keys periodically
	•	Use separate keys for development and production

🎛 Basic Controls
	•	Start/Stop Recording: Click mic icon or press Spacebar
	•	Reset Conversation: Click refresh icon
	•	Hide from Screen Sharing: Toggle “Hide” switch

🛠 Troubleshooting

If Angel can’t hear you:
	1.	Check system microphone settings
	2.	Ensure Angel has microphone permissions
	3.	Restart the app


rhis is one way of using things, if youbfind this process is to much hectic and looking somthing easy and robust we got you:


## Option 2️⃣ Lifetime Access Version (Easy One-Click Install + More Features)

### The Lifetime version is built for ppeoplewho want more options, more features, and a true one-click solution for their meetings or iinterviews— without touching the terminal or IDE. We’ve packed it to be as powerful and easy as possible, and yyoucan own it forever for just $69. OOnepayment. Lifetime peace of mind.

Prefer a ready-to-use app with more features?
The Lifetime Access Version is a one-time purchase — no coding required.

💎 Extra Features
	•	Supports OpenAI & Google Gemini API keys
	•	Token usage control
	•	Multiple chat modes (coding, brainstorming, Q&A)
	•	Voice + Chat combined in one interface
	•	Priority support
	•	Future updates iincluded
💰 Pricing
	•	One-time payment: $69 (~₹5,175 via Razorpay/PhonePe)
	•	Use forever — no monthly or yearly fees

📜 Bring Your Own API Policy

We never include an API key — you bringyour own from OpenAI or Gemini(free)

📥 Download & Test it for free:

https://lazyjobseeker.com/angel-lifetime

⸻

🔒 Privacy & Security
	•	Audio is processed in real-time and never stored
	•	Transcripts remain on your device
	•	API calls follow provider privacy policies
	•	Keys are never hard-coded

⸻

Visit website for mode details 
